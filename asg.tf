# ==========================
# Variables (tweak as needed)
# ==========================


# ==========================================
# ECS-optimized AMI (AL2) via SSM Parameter
# ==========================================
data "aws_ssm_parameter" "ecs_ami" {
  name = "/aws/service/ecs/optimized-ami/amazon-linux-2/recommended/image_id"
}

# ==================================================
# Launch Template (joins ECS cluster + CW Agent mem)
# ==================================================
resource "aws_launch_template" "ecs" {
  name_prefix   = "${var.name_prefix}-lt-"
  image_id      = data.aws_ssm_parameter.ecs_ami.value
  instance_type = var.od_instance_type

  iam_instance_profile { name = aws_iam_instance_profile.ecs_instance_profile.name }

  # Replace with your SG if different
  vpc_security_group_ids = [aws_security_group.ecs_instances.id]

  user_data = base64encode(<<-EOF
              #!/bin/bash
              set -eux
              echo ECS_CLUSTER=${aws_ecs_cluster.this.name} >> /etc/ecs/ecs.config
              systemctl enable --now ecs

              # Install & start CloudWatch Agent for memory metrics
              yum install -y amazon-cloudwatch-agent || true
              cat >/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json <<'AGCONF'
              {
                "metrics": {
                  "append_dimensions": {
                    "AutoScalingGroupName": "$${aws:AutoScalingGroupName}",
                    "InstanceId": "$${aws:InstanceId}"
                  },
                  "metrics_collected": {
                    "mem": { "measurement": ["mem_used_percent"], "metrics_collection_interval": 60 }
                  }
                }
              }
              AGCONF
              /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl \
                -a fetch-config -m ec2 \
                -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json -s || true
              EOF
  )
}

# ==========================
# On-Demand ASG
# ==========================
resource "aws_autoscaling_group" "od" {
  name                      = "${var.name_prefix}-od-asg"
  max_size                  = var.max_capacity
  min_size                  = var.min_capacity
  desired_capacity          = var.desired_capacity
  vpc_zone_identifier       = module.vpc.private_subnets
  health_check_type         = "EC2"
  health_check_grace_period = 120

  launch_template { id = aws_launch_template.ecs.id }

  tag {
    key                 = "Name"
    value               = "${var.name_prefix}-od"
    propagate_at_launch = true
  }
}

# Target tracking: CPU
resource "aws_autoscaling_policy" "od_cpu_target" {
  name                   = "${var.name_prefix}-od-cpu-tt"
  autoscaling_group_name = aws_autoscaling_group.od.name
  policy_type            = "TargetTrackingScaling"
  target_tracking_configuration {
    predefined_metric_specification { predefined_metric_type = "ASGAverageCPUUtilization" }
    target_value = 60
  }
}

# Optional: Memory target tracking (needs CW Agent)
resource "aws_autoscaling_policy" "od_mem_target" {
  count                  = var.enable_asg_memory_scaling ? 1 : 0
  name                   = "${var.name_prefix}-od-mem-tt"
  autoscaling_group_name = aws_autoscaling_group.od.name
  policy_type            = "TargetTrackingScaling"
  target_tracking_configuration {
    customized_metric_specification {
      metric_name = "mem_used_percent"
      namespace   = "CWAgent"
      statistic   = "Average"
      unit        = "Percent"
      dimensions {
        name  = "AutoScalingGroupName"
        value = aws_autoscaling_group.od.name
      }
    }
    target_value = 70
  }
}

# ==========================
# Spot ASG (Mixed Instances)
# ==========================
resource "aws_autoscaling_group" "spot" {
  name                = "${var.name_prefix}-spot-asg"
  max_size            = var.max_capacity
  min_size            = 0
  desired_capacity    = 0
  vpc_zone_identifier = module.vpc.private_subnets
  health_check_type   = "EC2"

  mixed_instances_policy {
    launch_template {
      launch_template_specification { launch_template_id = aws_launch_template.ecs.id }
    }
    instances_distribution {
      on_demand_percentage_above_base_capacity = 0
      spot_allocation_strategy                 = "capacity-optimized"
    }
    dynamic "override" {
      for_each = var.spot_instance_types
      content { instance_type = override.value }
    }
  }

  tag {
    key                 = "Name"
    value               = "${var.name_prefix}-spot"
    propagate_at_launch = true
  }
}

# Target tracking: CPU
resource "aws_autoscaling_policy" "spot_cpu_target" {
  name                   = "${var.name_prefix}-spot-cpu-tt"
  autoscaling_group_name = aws_autoscaling_group.spot.name
  policy_type            = "TargetTrackingScaling"
  target_tracking_configuration {
    predefined_metric_specification { predefined_metric_type = "ASGAverageCPUUtilization" }
    target_value = 60
  }
}

# Optional: Memory target tracking (needs CW Agent)
resource "aws_autoscaling_policy" "spot_mem_target" {
  count                  = var.enable_asg_memory_scaling ? 1 : 0
  name                   = "${var.name_prefix}-spot-mem-tt"
  autoscaling_group_name = aws_autoscaling_group.spot.name
  policy_type            = "TargetTrackingScaling"
  target_tracking_configuration {
    customized_metric_specification {
      metric_name = "mem_used_percent"
      namespace   = "CWAgent"
      statistic   = "Average"
      unit        = "Percent"
      dimensions { name = "AutoScalingGroupName" value = aws_autoscaling_group.spot.name }
    }
    target_value = 70
  }
}

# ==========================
# ECS Capacity Providers
# ==========================
resource "aws_ecs_capacity_provider" "od" {
  name = "${var.name_prefix}-cp-od"
  auto_scaling_group_provider {
    auto_scaling_group_arn         = aws_autoscaling_group.od.arn
    managed_scaling {
      status                    = "ENABLED"
      target_capacity           = 75
      minimum_scaling_step_size = 1
      maximum_scaling_step_size = 10
    }
    managed_termination_protection = "ENABLED"
  }
}

resource "aws_ecs_capacity_provider" "spot" {
  name = "${var.name_prefix}-cp-spot"
  auto_scaling_group_provider {
    auto_scaling_group_arn         = aws_autoscaling_group.spot.arn
    managed_scaling {
      status                    = "ENABLED"
      target_capacity           = 75
      minimum_scaling_step_size = 1
      maximum_scaling_step_size = 10
    }
    managed_termination_protection = "ENABLED"
  }
}

resource "aws_ecs_cluster_capacity_providers" "assoc" {
  cluster_name = aws_ecs_cluster.this.name
  capacity_providers = [
    aws_ecs_capacity_provider.od.name,
    aws_ecs_capacity_provider.spot.name
  ]
  default_capacity_provider_strategy = [
    { capacity_provider = aws_ecs_capacity_provider.od.name,   weight = 1, base = 1 },
    { capacity_provider = aws_ecs_capacity_provider.spot.name, weight = 1 }
  ]
}