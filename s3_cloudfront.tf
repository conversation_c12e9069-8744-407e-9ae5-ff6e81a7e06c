# --------------------------
# S3 Bucket (versioned, lifecycle)
# --------------------------
resource "aws_s3_bucket" "assets" {
  bucket = "${var.name_prefix}-assets-${var.region}"
}

resource "aws_s3_bucket_versioning" "assets" {
  bucket = aws_s3_bucket.assets.id
  versioning_configuration { status = "Enabled" }
}

resource "aws_s3_bucket_lifecycle_configuration" "assets" {
  bucket = aws_s3_bucket.assets.id
  rule {
    id     = "transition-after-1y"
    status = "Enabled"
    transition {
      days          = 365
      storage_class = "STANDARD_IA"
    }
  }
}

resource "aws_s3_bucket_ownership_controls" "assets" {
  bucket = aws_s3_bucket.assets.id
  rule { object_ownership = "BucketOwnerEnforced" }
}

resource "aws_s3_bucket_public_access_block" "assets" {
  bucket                  = aws_s3_bucket.assets.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# --------------------------
# CloudFront OAC + Signed URLs
# --------------------------
resource "aws_cloudfront_origin_access_control" "assets" {
  name                              = "${var.name_prefix}-assets-oac"
  description                       = "OAC for S3 origin"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}

resource "aws_cloudfront_public_key" "assets" {
  count       = var.cloudfront_public_key_pem == null ? 0 : 1
  comment     = "${var.name_prefix}-assets-public-key"
  encoded_key = var.cloudfront_public_key_pem
  name        = "${var.name_prefix}-assets-pubkey"
}

resource "aws_cloudfront_key_group" "assets" {
  count   = var.cloudfront_public_key_pem == null ? 0 : 1
  comment = "${var.name_prefix}-assets-key-group"
  items   = [aws_cloudfront_public_key.assets[0].id]
  name    = "${var.name_prefix}-assets-kg"
}

data "aws_cloudfront_cache_policy" "caching_optimized" {
  name = "Managed-CachingOptimized"
}

data "aws_cloudfront_response_headers_policy" "security_headers" {
  name = "Managed-SecurityHeadersPolicy"
}

resource "aws_cloudfront_distribution" "assets" {
  enabled             = true
  comment             = "${var.name_prefix} assets CDN"
  is_ipv6_enabled     = true
  default_root_object = null

  origin {
    domain_name              = aws_s3_bucket.assets.bucket_regional_domain_name
    origin_id                = "s3-assets"
    origin_access_control_id = aws_cloudfront_origin_access_control.assets.id
  }

  default_cache_behavior {
    target_origin_id           = "s3-assets"
    viewer_protocol_policy     = "redirect-to-https"
    allowed_methods            = ["GET", "HEAD"]
    cached_methods             = ["GET", "HEAD"]
    compress                   = true
    cache_policy_id            = data.aws_cloudfront_cache_policy.caching_optimized.id
    response_headers_policy_id = data.aws_cloudfront_response_headers_policy.security_headers.id

    dynamic "trusted_key_groups" {
      for_each = var.cloudfront_public_key_pem == null ? [] : [1]
      content { items = [aws_cloudfront_key_group.assets[0].id] }
    }
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = true
  }

  aliases = [var.assets_domain_name]
}

# --------------------------
# S3 Bucket Policy (only allow CloudFront OAC)
# --------------------------
data "aws_iam_policy_document" "assets_oac" {
  statement {
    actions   = ["s3:GetObject"]
    resources = ["${aws_s3_bucket.assets.arn}/*"]
    principals {
      type        = "Service"
      identifiers = ["cloudfront.amazonaws.com"]
    }
    condition {
      test     = "StringEquals"
      variable = "AWS:SourceArn"
      values   = [aws_cloudfront_distribution.assets.arn]
    }
  }
}

resource "aws_s3_bucket_policy" "assets" {
  bucket = aws_s3_bucket.assets.id
  policy = data.aws_iam_policy_document.assets_oac.json
}

# --------------------------
# Route53 record → CloudFront
# --------------------------
resource "aws_route53_record" "assets" {
  zone_id = var.route53_zone_id
  name    = var.assets_domain_name
  type    = "A"
  alias {
    name                   = aws_cloudfront_distribution.assets.domain_name
    zone_id                = aws_cloudfront_distribution.assets.hosted_zone_id
    evaluate_target_health = false
  }
}

# --------------------------
# Outputs
# --------------------------
output "assets_bucket_name" { value = aws_s3_bucket.assets.bucket }
output "assets_bucket_arn" { value = aws_s3_bucket.assets.arn }
output "cloudfront_id" { value = aws_cloudfront_distribution.assets.id }
output "cloudfront_domain" { value = aws_cloudfront_distribution.assets.domain_name }
output "assets_url" { value = "https://${var.assets_domain_name}" }
output "cloudfront_key_group_id" {
  value       = try(aws_cloudfront_key_group.assets[0].id, null)
  description = "If non-null, CloudFront requires signed URLs using this key group."
}

