# --------------------------
# Variables
# --------------------------
variable "name_prefix" {
  type    = string
  default = "ecs-ec2-ha"
}

variable "region" {
  default = "ap-northeast-1"
}

variable "route53_zone_id" {
  description = "Hosted zone ID for Route53"
  type        = string
}

variable "assets_domain_name" {
  description = "DNS name for CloudFront distribution (e.g., assets.example.com)"
  type        = string
  default     = "assets.example.com"
}

variable "cloudfront_public_key_pem" {
  description = "PEM-encoded RSA public key for CloudFront signed URLs (key pair you manage)"
  type        = string
  default     = null
}

variable "od_instance_type" {
  type    = string
  default = "t3.medium"
}

variable "spot_instance_types" {
  type    = list(string)
  default = ["t3.small", "t3a.small"]
}

variable "min_capacity" {
  type    = number
  default = 2
}

variable "desired_capacity" {
  type    = number
  default = 2
}

variable "max_capacity" {
  type    = number
  default = 6
}

variable "enable_asg_memory_scaling" {
  type    = bool
  default = false
}

